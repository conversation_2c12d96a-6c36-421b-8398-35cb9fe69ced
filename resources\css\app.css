@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom Typography Styles */
@layer base {
    /* Set default body font to Newsreader (serif) */
    body {
        @apply font-serif text-gray-900;
    }

    /* Set all headings to use Lexend (sans-serif) with bold weight */
    h1, h2, h3, h4, h5, h6 {
        @apply font-sans font-bold;
    }

    /* Specific heading styles */
    h1 {
        @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl tracking-tight;
    }

    h2 {
        @apply text-2xl sm:text-3xl md:text-4xl tracking-tight;
    }

    h3 {
        @apply text-xl sm:text-2xl md:text-3xl;
    }

    /* Paragraph styles */
    p {
        @apply font-serif leading-relaxed;
    }
}

@layer components {
    .pixelated {
        image-rendering: pixelated;
    }

    /* Button text should use sans-serif */
    button, .button {
        @apply font-sans;
    }

    /* Navigation links should use sans-serif */
    nav a {
        @apply font-sans;
    }
}