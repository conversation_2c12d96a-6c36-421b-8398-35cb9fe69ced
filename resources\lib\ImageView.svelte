<!--
 @component
 This component displays a small(er) image preview and a fullscreen modal popup that at a larger size plus relevant information about the image / piece.
-->

<!-- svelte-ignore unused-export-let -->
<script lang="ts">
    import Loading from "./Loading.svelte";
    import { onMount } from 'svelte';

    export let photo: any;
    export let catalogNum: number;
    export let imageIndex: number = 0;
    export let title: string = 'Title Unknown';
    export let description: string = 'Description Pending';
    export let location: string = 'Location Pending';
    export let encodedLocation: string;
    export let tags: string = 'None';
    export let eras: string ;
    export let year: string;
    export let artist: string = 'Unknown';
    export let artistMore: string = '';
    export let photographer: string ;
    export let photoDate: number;
    export let filename: string;
    export let foldername: string;
    export let fwNum: string;

    export let gallery: boolean = false;

    let photoArray: Array<any> = [];
    let catalog_groups: Record<number, any[]> = {};
    let totalImages: number = 0;
    let gallery_index = 0;
    let isModalVisible = false;
    let currentPhoto = photo; // To track current photo in the modal
    let imageLoading = true; // Flag for loading state
    let isImageFailed = false; // Flag for image load failure
    let real_path: string = ''; // Real image path
    let basePath = '/images'; // Base path for images
    let placeholder = `${basePath}/placeholder.webp`; // Placeholder image path

    onMount(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const modalParam = urlParams.get('catalogNum');
        if (modalParam) {
            const matchCatalog = parseInt(modalParam);
            if (matchCatalog === catalogNum && 1 === imageIndex) {
                toggleModalVisibility();
            }
        }
    });

    // Update photo array and group images by catalog number
    function addPhotos(images: any) {
        if (!Array.isArray(images)) {
            images = [images];
        }

        photoArray.push(...images);
        groupCatalogImages();
    }

    function groupCatalogImages() {
        catalog_groups = photoArray.reduce((groups, item) => {
            if (!groups[item.catalogNum]) {
                groups[item.catalogNum] = [];
            }
            groups[item.catalogNum].push(item);
            return groups;
        }, {});
        totalImages = photoArray.length;
    }

    // Ensure photo is wrapped in an array if it's not already
    $: {
        if (photo) {
            addPhotos(photo);
            updateImagePath();
        }
    }

    $: gallery_images = catalog_groups[catalogNum] || [];

    // Function to navigate gallery images
    async function navigateGallery(direction: number) {
        gallery_index += direction;
        if (gallery_index >= gallery_images.length) gallery_index = gallery_images.length - 1;
        if (gallery_index < 0) gallery_index = 0;

        if (gallery_images[gallery_index]) {
            currentPhoto = gallery_images[gallery_index];
            imageLoading = true;
            await updateImagePath();
        }
    }

    // Check if WebP image exists
    function checkImageExists(imagePath: string): Promise<boolean> {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => resolve(true);
            img.onerror = () => resolve(false);
            img.src = imagePath;
        });
    }

    // Set the real path for the image
    async function updateImagePath() {
        real_path = placeholder;
        const safe = (val) => encodeURIComponent(val || '');

        const cp = currentPhoto || {};

        // If location is Feature Walls
        // then use FW/FW#/foldername/filename
        
        let fwPath = '';
        let fwPathAlt = '';
        if (cp.location === 'Feature Walls') {
            // remove spaces from fwNum
            fwNum = cp.fwNum.replace(/\s/g, '');
            // real_path = `${basePath}/FW/${fwNum}/${cp.foldername}/${cp.filename}.webp`;
            // try webp and png
            fwPath = `${basePath}/FW/${fwNum}/${cp.foldername}/${cp.filename}.webp`;
            fwPathAlt = `${basePath}/FW/${fwNum}/${cp.foldername}/${cp.filename}.png`;
            imageLoading = true;
            return;
        }

        // Clean base values
        const rawArtist = cp.artist ?? artist ?? '';
        const rawFolder = cp.foldername ?? foldername ?? '';
        const rawArtistMore = (cp.artistMore ?? artistMore ?? '')
                .replace(/,\s*\(RIP\)/gi, '')
                .replace(/\(RIP\)/gi, '')
                .trim();
        const rawFile = cp.filename ?? filename ?? '';

        const safeArtist     = safe(rawArtist);
        const safeArtistMore = rawArtistMore ? safe(rawArtistMore) : '';
        const safeFolder     = safe(rawFolder);
        const safeFile       = safe(rawFile);

        const safeArtistAndMore = safe(rawArtistMore
            ? `${rawArtist}, ${rawArtistMore}`
            : rawArtist);
        
        const safeArtistAndMoreAlt = safe(rawArtistMore
            ? `${rawArtist} ${rawArtistMore}`
            : rawArtist);

        const basePaths = [
            `${safeArtist}/${safeFolder}/${safeFile}`,
            safeArtistMore ? `${safeArtistMore}/${safeFolder}/${safeFile}` : '',
            `${safeArtistAndMore}/${safeFolder}/${safeFile}`,
            `${safeArtistAndMoreAlt}/${safeFolder}/${safeFile}`,
            `${fwPath}`,
            `${fwPathAlt}`,
        ].filter(Boolean); // remove empty strings

        for (const path of basePaths) {
            for (const ext of ['webp', 'png']) {
                const fullPath = `${basePath}/${path}.${ext}`;
                console.log(`checking ${ext} path:`, fullPath);
                if (await checkImageExists(fullPath)) {
                    console.log(`found ${ext} path:`, fullPath);
                    real_path = fullPath;
                    imageLoading = true; // Means image is loaded
                    return;
                }
            }
        }
    }

    // re-run whenever your gallery images or index changes
    $: gallery_images, gallery_index, updateImagePath();

    // If year is empty, set it to eras
    $: displayYear = (!year || year.trim() === '') ? eras : year;

    // Make title capitalized
    $: title = title.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');

    // Ensure non-empty title and description
    $: title = title.trim() === ''
        ? 'Title Unknown'
        : title.split(' ').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' ');

    // Ensure non-empty title and description
    $: description = description.trim() === '' ? 'Description Pending' : description;

    // Close modal when clicked outside
    function closeModal(event: MouseEvent) {
        if (event.target === event.currentTarget) {
            isModalVisible = false;
        }
    }

    function toggleModalVisibility() {
        isModalVisible = !isModalVisible;
        if (gallery_images.length > 0 && gallery_index < gallery_images.length) {
            currentPhoto = gallery_images[gallery_index];
        } else {
            currentPhoto = photo; // fallback to the initial photo
        }
    }


    $: modalName = `${catalogNum}-${imageIndex}`;
</script>

<!-- svelte-ignore a11y-click-events-have-key-events -->
<!-- svelte-ignore a11y-no-noninteractive-element-interactions -->

<!-- Thumbnail View -->
{#if imageIndex === 1}
    <article on:click={toggleModalVisibility} class="cursor-pointer flex flex-col rounded-md bg-white border border-black shadow-lg transition-all duration-150 hover:ring-4 overflow-hidden h-full">
        <div class="relative">
            <!-- Loading Spinner with Background Blur -->
            {#if imageLoading && !isImageFailed}
                <Loading scale={2} left={15} />
            {/if}

            <!-- Image -->
            <img
                src={real_path}
                alt={description}
                class="bg-gray-500 w-full object-cover self-center h-48 sm:h-56"
                id="ctlgnm-{catalogNum}"
                on:load={() => imageLoading = false}
                on:error={() => {
                    isImageFailed = true;
                    imageLoading = false;
                }}
                on:click|stopPropagation={toggleModalVisibility}
            />
        </div>
        <div class="p-3 sm:p-4 text-ellipsis">
            <h1 class="text-lg sm:text-xl font-bold truncate">{artist}</h1>
            <p class="text-sm sm:text-base truncate">{title}</p>
        </div>
    </article>
{/if}

<!-- svelte-ignore a11y-click-events-have-key-events -->
<!-- svelte-ignore a11y-no-noninteractive-element-interactions -->

<!-- Gallery View -->
<dialog open={isModalVisible} on:click|stopPropagation={closeModal} class="{isModalVisible ? 'flex justify-center items-center w-screen h-screen z-50 bg-black/20' : 'invisible'} fixed top-0 left-0" id="mdl-{modalName}">
    <div class="p-2 sm:p-4 m-2 sm:m-4 bg-white h-[90vh] sm:h-[80vh] w-[95vw] sm:w-[90vw] md:w-[85vw] flex flex-col rounded-md shadow-lg shadow-black">
        <!-- Image section - Now at the top for both mobile and desktop -->
        <div class="flex flex-1 h-[50vh] mx-auto pixelated object-contain content-center relative mb-4">
            <!-- Loading Spinner with Background Blur -->
            {#if imageLoading && !isImageFailed}
                <Loading scale={4} left={30} />
            {/if}

            <!-- Image -->
            <img
                src={real_path}
                id = "indx-{currentPhoto.imageIndex}"
                alt={currentPhoto.description}
                class="mx-auto pixelated object-contain max-h-full max-w-full"
                on:load={() => imageLoading = false}
                on:error={() => {
                    isImageFailed = true;
                    imageLoading = false;
                }}
            />
        </div>

        <!-- Info section - Now below the image -->
        <div class="flex flex-col p-2 sm:p-4 w-full overflow-y-auto">
            <h1 class="font-sans text-xl sm:text-2xl font-bold break-words tracking-tight">{title}</h1>
            <p class="font-serif text-base sm:text-lg leading-relaxed">
                by
                {#if currentPhoto && currentPhoto.artist && currentPhoto.artist.includes(',')}
                    {#each currentPhoto.artist.split(', ') as single_artist}
                        <a data-sveltekit-reload href="/artist/{single_artist}" class="underline hover:decoration-wavy">{single_artist},</a>
                    {/each}
                {:else}
                    {#if currentPhoto && currentPhoto.artist}
                        <a data-sveltekit-reload href="/artist/{currentPhoto.artist}" class="underline hover:decoration-wavy">{currentPhoto.artist}</a>
                    {:else}
                        <span>Artist Unknown</span>
                    {/if}
                {/if}
            </p>

            <!-- if {displayYear} is null/empty, leave blank -->
            {#if displayYear && displayYear !== '' && displayYear !== 'Unknown' && displayYear !== null}
                <p id="year" class="font-serif text-sm sm:text-base leading-relaxed">{displayYear}</p>
            {/if}
            <!-- <p class="font-serif text-sm sm:text-base leading-relaxed">{displayYear}</p> -->
            {#if photographer && photographer !== '' && photographer !== 'Unknown' && photographer !== null}
                <p id="photographer" class="font-serif text-xs sm:text-sm leading-relaxed">
                    Photograph by {photographer}
                    {#if photoDate && photoDate !== null}
                        , {photoDate}
                    {/if}
                </p>
            {/if}
            <!-- <p class="font-serif text-xs sm:text-sm leading-relaxed">Photograph by {currentPhoto.photographer}, {currentPhoto.photoDate || 'Unknown'}</p> -->
            <p class="font-serif text-sm sm:text-base leading-relaxed">{currentPhoto.location}</p>

            {#if currentPhoto.tags && currentPhoto.tags !== ''}
                <div class="my-2 sm:my-4">
                    <span class="font-sans font-bold text-sm sm:text-base">Tags: </span>
                    <div class="flex flex-wrap gap-1 sm:gap-2 mt-1 justify-center">
                        {#each currentPhoto.tags.split(', ') as tag}
                            <span class="inline-block whitespace-nowrap px-2 sm:px-3 py-1 sm:py-2 bg-blue-600/40 rounded-full text-xs sm:text-sm">
                                <a data-sveltekit-reload class="font-sans hover:underline" href="/tag/{tag}">{tag}</a>
                            </span>
                        {/each}
                    </div>
                </div>
            {/if}

            <p class="font-serif text-xs sm:text-sm mt-2 leading-relaxed">{currentPhoto.description}</p>
            {#if gallery_images.length > 1}
                <div class="flex gap-2 mt-2 justify-start items-center py-2">
                    <button class="font-sans p-1 sm:p-2 text-sm" on:click|stopPropagation={() => navigateGallery(-1)} disabled={gallery_index === 0}>Prev</button>
                    <button class="font-sans p-1 sm:p-2 text-sm" on:click|stopPropagation={() => navigateGallery(1)} disabled={gallery_index >= gallery_images.length - 1}>Next</button>
                    <span class="font-sans text-sm">{gallery_index + 1} / {gallery_images.length}</span>
                </div>
            {/if}
        </div>
    </div>
</dialog>

<style lang="postcss">
    button {
        @apply border border-black rounded-md shadow-lg bg-white hover:bg-slate-800 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed;
    }
</style>