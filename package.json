{"private": true, "type": "module", "scripts": {"start": "node toggle-env.js", "setup:init": "node toggle-env.js --setup", "setup:dev": "node toggle-env.js --dev", "setup:prod": "node toggle-env.js --prod", "dev": "vite dev", "build": "rmdir /S /Q build public\\build > nul && vite build && xcopy /E /Y /I build public\\build > nul || vite build && xcopy /E /Y /I build public\\build > nul"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/sqlite3": "^5.1.0", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "vite": "^5.4.14", "vite-plugin-static-copy": "^2.3.0"}, "dependencies": {"@inertiajs/inertia": "^0.11.1", "@inertiajs/inertia-svelte": "^0.8.0", "@inertiajs/svelte": "^1.3.0", "@sveltejs/vite-plugin-svelte": "^3.1.2", "npm": "^11.1.0", "panzoom": "^9.4.3", "sql.js": "^1.8.0", "sqlite3": "^5.1.7", "svelte": "^4.2.19", "svelte-routing": "^2.13.0"}}