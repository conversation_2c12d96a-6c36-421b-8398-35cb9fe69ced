# Rochester Subway

This website is an archive of the graffiti art in the Rochester Subway, intended to preserve the rich cultural & artistic history of the location.

## Contributing to this project

The recommended way to get this repository on your machine is to first install [git](https://git-scm.com/), or a visual git client such as [GitHub Desktop](https://desktop.github.com/).


![img.png](img.png)

To checkout (download) the repo, navigate to the green `code` button as shown above, then choose your preferred method to clone. (If you are not sure what to pick, use HTTPS.) copy the link, and run `git clone <link>` in your terminal, replaceing \<link> with the one you copied.

### Adding your contributions

In order to add your contributions to this project, you will need to create a new branch. This can be done by running `git checkout -b <branch-name>`, replacing \<branch-name> with a descriptive name for your branch.

In order to push changes to this repository, you will need to have a GitHub account. Check the GitHub documentation for details on configuring your git install with your login info.


## Developing
This project uses the following:
| Frameworks | Tools | Package Managers |
| --- | --- | --- |
| [<PERSON><PERSON>](https://laravel.com/) | [pnpm](https://pnpm.io/) | [Composer](https://getcomposer.org/) |
| [Svelte](https://svelte.dev/) | [Yarn](https://yarnpkg.com/) | [Artisan](https://laravel.com/docs/8.x/artisan) |
| [Inertia.js](https://inertiajs.com/) | | [PHP](https://www.php.net/) |
| [Tailwind CSS](https://tailwindcss.com/) | | |
| [Vite](https://vitejs.dev/) | | |

*check [package.json](./package.json) for more information*

<!-- dropdown -->

## Simple setup, Running, and Building
| Command | Description |
| --- | --- |
| `pnpm run setup:init` | Runs [Setting up the project](#setting-up-the-project)|
| `pnpm run setup:dev` | Changes variables to [dev environemnt](#devevlopment-environment) |
| `pnpm run setup:prod` | Changes variables to [prod environemnt](#production-environment) |

Run `pnpm start` to see this message in the terminal.

### Setting up the project
Running `pnpm run setup:init` does the following:
| Command | Description |
| --- | --- | 
| `cp .env.example .env` | Copy the .env file |
| `composer install` | Install PHP dependencies |
| `php artisan` | Check if the installation was successful |
| `pnpm install` | Install JS dependencies |
| `php artisan key:generate` | Generate a new key for the application |
| `php artisan inertia:middleware` | Generate the inertia middleware |

### Running the project
*Both commands need to be run in separate terminals.*
| Command | Description |
| --- | --- |
| `php artisan serve` | Start the server |
| `pnpm run dev` | Start the development server |
*https://localhost:8000*

<br><br>

*`yarn` or `npm` can be used in place of `pnpm` if you prefer.*

# Uploading to [rocsubway.rit.edu](https://rocsubway.cad.rit.edu/)
1. `pnpm run setup:prod` | change the environment to productio
2. `pnpm run build` | build the JS and CSS
3. Goto [CAD portal](https://request.cad.rit.edu/groupweb/641)
    - SFTP
    - hostname: `cad.rit.edu` : port: `22`
    - username: `rocsubway`
    - password: ...
4. Get SFTP credentials, Host, Username, Password
5. Use WinSCP / FileZilla to connect to the server (SFTP, port: 22)
6. To update the site:
    - Upload `public` folder to `public_html`[^1]
    - Upload `build` folder to `public_html`
    - Upload `resources` folder to `public_html`
        - :warning: If you change anything else, upload the root folder of that as well.[^2]
        *ie. if you make changes to `routes/web.php`, upload the `routes` folder to /home/<USER>/public_html/.*
> [!IMPORTANT]
> :warning: This ***only*** needs to be done the first time making the website :warning:
>    - > Upload the following folders to the `public_html` directory:
>        - > `app`, `bootstrap`, `config`, `database`, `public`, `resources`, `routes`, `storage`, `vendor`
>    - > From the `public` folder, upload the `index.php` file to the `public_html` directory.

>[!CAUTION]
> DO NOT upload the `node_modules` folder or `hot` file to the server. This is only used for development and will break the site if uploaded.

[^1]: /home/<USER>/public_html/
[^2]: ie. if you make changes to `routes/web.php`, upload the `routes` folder to `public_html`[^1].

### Other Notes
| What | How/Where |
| --- | --- |
| Make new url | `/qwert/[qwertName]` |
| Add new function | `app/Http/Controlers/AppController.php` |
| Add new folder | `resources/js/Pages/qwert/[qwertName]` |
| Add new route | `routes/web.php` |
| Add scripts | `resources/js/app.js` |
| Add styles | `resources/css/app.css`[^3] |
[^3]: Using Tailwind CSS, no need to add styles to `app.css` unless custom styles are needed, even then just add `<style lang="postcss">` to the end of the `.svelte` file.


<details> 
<summary> Devevlopment Environment </summary>

| New Value | File |
| --- | --- |
| `'url' => env('APP_URL', 'http://localhost'),` | `config/app.php` |
| `'url' => env('APP_ENV', 'development'),` | `config/app.php` |
| `require __DIR__.'/../vendor/autoload.php';` | `public/index.php` |
| `(require_once __DIR__.'/../bootstrap/app.php')` | `public/index.php` |
</details>

<details>
<summary>Production Environment</summary>

| New Value | File |
| --- | --- |
| `'url' => env('APP_URL', 'https://rocsubway.cad.rit.edu/'),` | `config/app.php` |
| `'url' => env('APP_ENV', 'production'),` | `config/app.php` |
| `require __DIR__.'/vendor/autoload.php';` | `public/index.php` |
| `(require_once __DIR__.'/bootstrap/app.php')` | `public/index.php` |
</details>

| Error | Solution|
| --- | --- |
|`Error fetching SQLite data: Error: both async and sync fetching of the wasm failed` | make sure `sql-wasm.wasm` is in public folder |
| `select * from "sessions" where "id" =` | check `.env` file for `SESSION_DRIVER=database` |
| `SQLSTATE[HY000]: General error: 1 no such table: sessions` | `database.php` change url `DB_URL` -> `DATABASE_URL`[^4] |

[^4]: Follow this French Arabic tutorial for more fixes: https://www.youtube.com/watch?v=tpgoQu8NAZw