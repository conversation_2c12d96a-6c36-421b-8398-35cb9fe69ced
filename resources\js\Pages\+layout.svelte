<script context="module">
	let navInitialized = false;
</script>

<script>
	import '../../css/app.css';
	import { Router, Route } from 'svelte-routing';
	import { onMount } from 'svelte';
	const routes = [
		{ title: 'Graffiti Map', href: '/map' },
		{ title: 'Court St', href: '/region/Court St' },
		{ title: 'West Main', href: '/region/West Main' },
		{ title: 'Long Tunnel', href: '/region/Long Tunnel' },
		{ title: 'City Hall', href: '/region/City Hall' },
		{ title: 'Aqueduct', href: '/region/Aqueduct' },
		{ title: 'Feature Walls', href: '/region/Feature Wall' },
		{ title: 'Rundel Area', href: '/region/Rundel Area' },
	];

	const mainRoute = routes[0];
	const regionRoutes = routes.slice(1);
	const basePath = '/images';

	const colors = ['red', 'emerald', 'blue'];
	const randomColor = `hover:text-${colors[Math.floor(Math.random() * colors.length)]}-600`;

    let isMobileMenuOpen = false;
    let isDesktopDropdownOpen = false;
    let isHovered = false;

	$: isMap = mainRoute === '/map';

    function toggleMobileMenu() {
        isMobileMenuOpen = !isMobileMenuOpen;
    }

	function toggleDesktopDropdown(event) {
		event.stopPropagation(); // stop the click from reaching document
		isDesktopDropdownOpen = !isDesktopDropdownOpen;
	}

	function handleClickOutside(event) {
		const dropdown = document.querySelector('.desktop-dropdown');
		if (dropdown && !dropdown.contains(event.target)) {
			// Delay to let button click finish first
			setTimeout(() => {
				isDesktopDropdownOpen = false;
			}, 100);
		}
	}

		// Use the module-scoped flag
		let showNav = false;

		if (!navInitialized) {
			showNav = true;
			navInitialized = true;
		}

    onMount(() => {
        document.addEventListener('click', handleClickOutside);
        return () => document.removeEventListener('click', handleClickOutside);
    });
</script>

{#if showNav}
<nav class="fixed top-0 left-0 w-full px-4 h-16 bg-black text-white flex gap-4 items-center z-50">
	<a href="/" class="text-2xl">
		<img src="{basePath}/RSGA.png" alt="Rochester Subway Graffiti Archive" class="h-12 w-12" />
	</a>

	<!-- Mobile menu toggle -->
	<button
		class="md:hidden ml-auto text-white focus:outline-none"
		on:click={toggleMobileMenu}
		aria-label="Toggle menu"
	>
		<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d={isMobileMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"} />
		</svg>
	</button>

	<!-- Desktop navigation -->
	<div class="hidden md:flex items-center ml-auto gap-4">
		<a href={mainRoute.href} class={`font-bold hover:underline ${randomColor}`}>{mainRoute.title}</a>

		<!-- svelte-ignore a11y-no-static-element-interactions -->
		<div class="relative desktop-dropdown"
        on:mouseenter={() => isHovered = true}
        on:mouseleave={() => isHovered = false}>
			<button
				class={`font-bold hover:underline py-5 ${randomColor}`}
				on:click={toggleDesktopDropdown}
			>
				Regions
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="h-4 w-4 inline-block ml-1 transform transition-transform duration-200"
					class:rotate-180={isDesktopDropdownOpen}
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
				</svg>
			</button>
	

			{#if isDesktopDropdownOpen || isHovered}
				<div class="absolute right-0 bg-black text-white shadow-lg rounded z-50 min-w-[10rem] max-w-[90vw]">
					{#each regionRoutes as route (route.title)}
						<a href={route.href} title={route.title} class="block font-bold px-4 py-2 border-b border-gray-700 hover:bg-gray-800 whitespace-nowrap">
							{route.title}
						</a>
					{/each}
				</div>
			{/if}
		</div>
	</div>
</nav>
{/if}

<!-- Mobile menu -->
{#if isMobileMenuOpen}
	<div class="fixed top-16 left-0 w-full bg-black text-white z-40 shadow-lg">
		<div class="flex flex-col p-4">
			<a href={mainRoute.href} class="font-bold py-2 border-b border-gray-700 hover:bg-gray-800">{mainRoute.title}</a>
			<span class="py-2 text-sm text-gray-400">Regions</span>
			<div class="bg-black text-white rounded shadow-lg mt-2 w-full">
				{#each regionRoutes as route}
					<a href={route.href} title={route.title} class="block font-bold px-4 py-2 border-b border-gray-700 hover:bg-gray-800 whitespace-nowrap">
						{route.title}
					</a>
				{/each}
			</div>
		</div>
	</div>
{/if}

<!-- Main Content -->
<!-- <div class="text-center pt-16">
	<main class="flex w-full justify-center items-center">
		<Router>
			{#each routes as route}
				<Route path={route.href}/>
			{/each}
		</Router>
		<slot />
	</main>
</div> -->

<div class={isMap ? 'text-center pt-16' : 'main-wrapper'}>
	<main class="flex w-full justify-center items-center">
		<Router>
			{#each routes as route}
				<Route path={route.href}/>
			{/each}
		</Router>
		<slot />
	</main>
</div>

<style lang="postcss">
	a {
		@apply font-sans text-lg min-w-fit hover:underline;
	}
	div {
		@apply transition-all duration-300 ease-in-out;
	}
	.main-wrapper {
		margin-top: 2rem;
    	overflow: hidden;

	}
</style>

